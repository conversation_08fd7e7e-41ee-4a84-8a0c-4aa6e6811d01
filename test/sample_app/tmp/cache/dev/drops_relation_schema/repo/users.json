{"digest": "B1989009233E0EA9AECF040845FE0F6B2D0C80B919361A87C51937B74F4F2165", "schema": {"attributes": {"fields": [{"attributes": {"meta": {"default": null, "type": ["atom", "integer"], "source": ["atom", "id"], "nullable": true, "primary_key": true, "foreign_key": false, "check_constraints": []}, "name": ["atom", "id"], "type": ["atom", "id"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "type": ["atom", "string"], "source": ["atom", "email"], "nullable": false, "primary_key": false, "foreign_key": false, "check_constraints": []}, "name": ["atom", "email"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "type": ["atom", "string"], "source": ["atom", "first_name"], "nullable": true, "primary_key": false, "foreign_key": false, "check_constraints": []}, "name": ["atom", "first_name"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "type": ["atom", "string"], "source": ["atom", "last_name"], "nullable": true, "primary_key": false, "foreign_key": false, "check_constraints": []}, "name": ["atom", "last_name"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "type": ["atom", "integer"], "source": ["atom", "age"], "nullable": true, "primary_key": false, "foreign_key": false, "check_constraints": []}, "name": ["atom", "age"], "type": ["atom", "integer"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": true, "type": ["atom", "boolean"], "source": ["atom", "is_active"], "nullable": true, "primary_key": false, "foreign_key": false, "check_constraints": []}, "name": ["atom", "is_active"], "type": ["atom", "boolean"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "type": ["atom", "string"], "source": ["atom", "profile_data"], "nullable": true, "primary_key": false, "foreign_key": false, "check_constraints": []}, "name": ["atom", "profile_data"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": "[]", "type": ["atom", "string"], "source": ["atom", "tags"], "nullable": true, "primary_key": false, "foreign_key": false, "check_constraints": []}, "name": ["atom", "tags"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "type": ["atom", "decimal"], "source": ["atom", "score"], "nullable": true, "primary_key": false, "foreign_key": false, "check_constraints": []}, "name": ["atom", "score"], "type": ["atom", "decimal"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "type": ["atom", "string"], "source": ["atom", "birth_date"], "nullable": true, "primary_key": false, "foreign_key": false, "check_constraints": []}, "name": ["atom", "birth_date"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "type": ["atom", "string"], "source": ["atom", "last_login_at"], "nullable": true, "primary_key": false, "foreign_key": false, "check_constraints": []}, "name": ["atom", "last_login_at"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "type": ["atom", "string"], "source": ["atom", "inserted_at"], "nullable": false, "primary_key": false, "foreign_key": false, "check_constraints": []}, "name": ["atom", "inserted_at"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "type": ["atom", "string"], "source": ["atom", "updated_at"], "nullable": false, "primary_key": false, "foreign_key": false, "check_constraints": []}, "name": ["atom", "updated_at"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}], "source": ["atom", "users"], "primary_key": {"attributes": {"fields": [{"attributes": {"meta": {"default": null, "type": ["atom", "integer"], "source": ["atom", "id"], "nullable": true, "primary_key": true, "foreign_key": false, "check_constraints": []}, "name": ["atom", "id"], "type": ["atom", "id"], "source": null}, "__struct__": "Field"}]}, "__struct__": "<PERSON><PERSON><PERSON>"}, "foreign_keys": [], "indices": {"attributes": {"indices": [{"attributes": {"name": ["atom", "users_is_active_index"], "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {"default": true, "type": ["atom", "boolean"], "source": ["atom", "is_active"], "nullable": true, "primary_key": false, "foreign_key": false, "check_constraints": []}, "name": ["atom", "is_active"], "type": ["atom", "boolean"], "source": null}, "__struct__": "Field"}], "unique": false}, "__struct__": "Index"}, {"attributes": {"name": ["atom", "users_last_name_first_name_index"], "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {"default": null, "type": ["atom", "string"], "source": ["atom", "first_name"], "nullable": true, "primary_key": false, "foreign_key": false, "check_constraints": []}, "name": ["atom", "first_name"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "type": ["atom", "string"], "source": ["atom", "last_name"], "nullable": true, "primary_key": false, "foreign_key": false, "check_constraints": []}, "name": ["atom", "last_name"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}], "unique": false}, "__struct__": "Index"}, {"attributes": {"name": ["atom", "users_email_index"], "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {"default": null, "type": ["atom", "string"], "source": ["atom", "email"], "nullable": false, "primary_key": false, "foreign_key": false, "check_constraints": []}, "name": ["atom", "email"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}], "unique": true}, "__struct__": "Index"}]}, "__struct__": "Indices"}}, "__struct__": "<PERSON><PERSON><PERSON>"}}